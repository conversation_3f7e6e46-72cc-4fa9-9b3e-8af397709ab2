/* File: ContactUs.module.css */

.contactSection {
  background-color: #fff;
  padding: 6rem 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  box-sizing: border-box;
}

.wrapper {
  max-width: 700px;
  width: 100%;
  text-align: center;
  animation: fadeInUp 1s ease forwards;
}

.title {
  font-size: 2.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin-bottom: 3rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.inputGroup {
  position: relative;
}

.input {
  width: 100%;
  font-size: 1rem;
  padding: 1.25rem 1rem 0.75rem;
  border: none;
  border-bottom: 2px solid #e5e7eb;
  outline: none;
  background: transparent;
  transition: border-color 0.3s ease;
  color: #111827;
}

.input::placeholder {
  color: #9ca3af;
  transition: opacity 0.3s;
}

.input:focus::placeholder {
  opacity: 0.3;
}

.textarea {
  resize: none;
  border-bottom: 2px solid #e5e7eb;
}

.focusBorder {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #000;
  transition: 0.4s ease;
}

.input:focus + .focusBorder {
  left: 0;
  width: 100%;
}

.button {
  background-color: #000;
  color: #fff;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: background 0.3s ease;
}

.button:hover {
  background-color: #222;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.successModal {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.successModal h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.successModal p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.dashboardLink {
  background: linear-gradient(135deg, #000, #333);
  color: white;
  text-decoration: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.dashboardLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.modalCloseButton {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modalCloseButton:hover {
  background: #4b5563;
  transform: translateY(-2px);
}

@media (max-width: 600px) {
  .title {
    font-size: 2rem;
  }
  .subtitle {
    font-size: 1rem;
  }

  .modalActions {
    flex-direction: column;
  }
}
