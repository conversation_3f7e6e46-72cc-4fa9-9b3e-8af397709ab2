'use client';
import styles from './FeatureVideos.module.css';
import { useEffect, useRef, useState } from 'react';

const services = [
  {
    name: 'Baltar Technologies',
    tagline: 'Web Infrastructure | Digital Branding | Automation',
    video: '/video/technology.mp4',
    subsidiaries: [
      {
        name: 'Frontend Web Design & Hosting',
        desc: 'We offer full-spectrum web services: design, development, domain registration, hosting, maintenance, and optimization. From basic business sites to complex platforms, we design and manage your web presence end-to-end.',
        cta: 'Explore Designs',
        features: [
          'High-performance web hosting on secure Canadian and international servers',
          'Domain setup, SSL, CDN, and email integration',
          'SEO-optimized, mobile-responsive websites with fast load times',
          'Client dashboard for support, invoicing, status tracking, and backups'
        ]
      },
      {
        name: 'Cre8ive Studio',
        desc: 'Our graphic design arm provides branding, creative direction, and print-ready production. We help brands come to life visually—with intention, precision, and consistency.',
        cta: 'Go to Studio',
        features: [
          'Logo design, brand kits, marketing materials, packaging, and signage',
          'Interactive portfolio showcasing past client work',
          'AI-powered brand generator for fast visual concepts',
          'Print store for business cards, flyers, brochures, and banners'
        ]
      },
      {
        name: 'Transac',
        desc: 'Digital receipts and loyalty tools for businesses. Put transac under Baltar technologies.',
        cta: 'Visit Transac',
        features: [
          'Digital receipt management',
          'Customer loyalty programs',
          'Business analytics dashboard',
          'Payment processing integration'
        ]
      },
    ],
  },
  {
    name: 'Baltar Hospitality',
    tagline: 'Catering | Bartending | Event Staffing & Planning',
    video: '/video/hospitality.mp4',
    subsidiaries: [
      {
        name: 'Savour & Sip',
        desc: 'Catering and event solutions for private parties, weddings, and corporate events—plus restaurant and kitchen staffing. We make events seamless, elegant, and fully serviced—with food, people, and logistics all covered.',
        cta: 'Dine With Us',
        features: [
          'Fully customized catering menus through our interactive menu builder',
          'Bartender and chef-for-hire services for events or private dinners',
          'Event package bookings, rental add-ons, and custom quotes',
          'Hospitality staffing for short-term or permanent placement'
        ]
      },
    ],
  },
  {
    name: 'Archon Engineering',
    tagline: 'Civil & Structural Engineering | Design | Consulting',
    video: '/video/consulting.mp4',
    subsidiaries: [
      {
        name: 'Archon Engineering',
        desc: 'A full-service civil and structural engineering firm, delivering residential and commercial design, permitting, and consultancy services. Whether you\'re building a home or developing a property, Archon makes the process streamlined, transparent, and intelligent.',
        cta: 'Consult Us',
        features: [
          'Custom home design, renovations, and structural assessments',
          'Digital project estimator with instant quoting',
          '3D design previews and interactive walkthroughs',
          'Secure client portal for drawings, permits, contracts, and progress updates'
        ]
      },
    ],
  },
  {
    name: 'Baltar Finance',
    tagline: 'Wealth Management | Market Tools | Financial Education',
    video: '/video/finance.mp4',
    subsidiaries: [
      {
        name: 'Baltar Wealth Management',
        desc: 'Providing practical financial tools and personalized guidance for individuals, families, and businesses. Designed for those who want control, insight, and clarity in their financial strategy.',
        cta: 'Manage Wealth',
        features: [
          'AI-driven investment risk assessment tailored to your goals',
          'Live North American market dashboard with real-time updates',
          'Automated reports on portfolio health and opportunities',
          'Custom service plans for clients needing private consulting'
        ]
      },
    ],
  },
  {
    name: 'Baltar Fashion',
    tagline: 'Luxury Retail | Fashion Tech | Subscription Style',
    video: '/video/fashion.mp4',
    subsidiaries: [
      {
        name: 'VR (Eyewear & Fashion Tech)',
        desc: 'A fashion-tech brand combining online luxury with real-time interactivity. We make premium style personal, immersive, and accessible.',
        cta: 'View Collections',
        features: [
          'Augmented reality try-on for eyewear and accessories',
          'AI-driven recommendations based on face shape and style history',
          'Fully integrated loyalty system with rewards, referrals, and discounts',
          'Secure e-commerce checkout with Stripe, Square, and Apple Pay'
        ]
      },
      {
        name: 'Le Mode Co.',
        desc: 'Apparel and accessories with a curated, data-backed shopping experience. Fashion, delivered smart—powered by AI, styled by people.',
        cta: 'Explore Brand',
        features: [
          'Style quizzes and curated fashion boxes delivered monthly',
          'Digital lookbooks showcasing new seasonal collections',
          'Integrated influencer management and affiliate tracking',
          'Flexible subscriptions and exclusive capsule drops'
        ]
      },
    ],
  },
  {
    name: 'Baltar Media',
    tagline: 'Research | News | Digital Reach',
    video: '/video/media.mp4',
    subsidiaries: [
      {
        name: 'Consumer Pulse',
        desc: 'Real-time polling, audience research, and sentiment analysis tools for businesses and organizations. Get clarity on what your audience really thinks—backed by live data.',
        cta: 'Pulse Insights',
        features: [
          'AI-analyzed consumer surveys and feedback loops',
          'Real-time dashboards with downloadable reports',
          'Business-tier API access for ongoing analytics integration',
          'Data-driven insights to improve products, marketing, and service design'
        ]
      },
      {
        name: 'Zeitgeist Media',
        desc: 'A digital content platform that delivers personalized news, audio, and visual experiences. Where content meets automation—Zeitgeist helps brands and users stay ahead of the curve.',
        cta: 'View Showcase',
        features: [
          'AI-curated news feed customized by user behavior',
          'Auto-generated podcasts and video series',
          'Monetized ad space for aligned brands and sponsors',
          'Community insights and public engagement tools'
        ]
      },
    ],
  },
];

export default function FeatureVideos() {
  const videoRefs = useRef([]);
  const cardRefs = useRef([]);
  const sectionRef = useRef(null);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const videoObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const target = entry.target;
          if (entry.isIntersecting) {
            target.classList.add(styles.zoomIn);
          } else {
            target.classList.remove(styles.zoomIn);
          }
        });
      },
      { threshold: 0.3 }
    );

    const cardObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              entry.target.classList.add(styles.slideInUp);
            }, index * 100);
          }
        });
      },
      { threshold: 0.1 }
    );

    videoRefs.current.forEach((el) => el && videoObserver.observe(el));
    cardRefs.current.forEach((el) => el && cardObserver.observe(el));

    return () => {
      videoRefs.current.forEach((el) => el && videoObserver.unobserve(el));
      cardRefs.current.forEach((el) => el && cardObserver.unobserve(el));
    };
  }, []);

  const getHref = (name) => {
    const lower = name.toLowerCase();
    if (lower === 'transac') return '/transac';
    if (lower === 'frontend web design & hosting') return '/frontend-web-design';
    if (lower === 'savour & sip') return '/sip-and-savour';
    if (lower === 'le mode co.') return '/le-mode-co';
    if (lower === 'vr (eyewear & fashion tech)') return '/vr';
    if (lower === 'consumer pulse') return '/consumer-pulse';
    if (lower === 'archon engineering') return '/coming-soon';
    if (lower === 'cre8ive studio') return '/coming-soon';
    if (lower === 'baltar wealth management') return '/coming-soon';
    if (lower === 'zeitgeist media') return '/coming-soon';
    return '/coming-soon';
  };

  return (
    <section
      ref={sectionRef}
      className={styles.featureSection}
      style={{
        transform: `translateY(${scrollY * 0.1}px)`,
      }}
    >
      <div className={styles.sectionHeader}>
        <h2 className={styles.sectionHeading}>Work With Baltar Inc.</h2>
        <p className={styles.sectionSubheading}>
          Integrated Services. Intelligent Execution.
        </p>
        <p className={styles.sectionDescription}>
          Whether you're a business, homeowner, investor, or startup—Baltar offers streamlined
          services, automated systems, and hands-on support to get you further, faster.
        </p>
      </div>

      {services.map((service, index) => (
        <div key={index} className={styles.serviceBlock}>
          <div className={styles.serviceHeader}>
            <h3 className={styles.serviceTitle}>{service.name}</h3>
            {service.tagline && (
              <p className={styles.serviceTagline}>{service.tagline}</p>
            )}
          </div>

          <video
            ref={(el) => (videoRefs.current[index] = el)}
            src={service.video}
            className={styles.video}
            autoPlay
            muted
            loop
            playsInline
          />

          <div className={styles.subsidiaries}>
            {service.subsidiaries.map((sub, i) => {
              const href = getHref(sub.name);
              const openInNewTab = [
                'transac',
                'frontend web design & hosting',
                'savour & sip',
                'le mode co.',
                'vr (eyewear & fashion tech)',
                'consumer pulse'
              ].includes(sub.name.toLowerCase());

              const cardIndex = index * service.subsidiaries.length + i;

              return (
                <div
                  key={i}
                  ref={(el) => (cardRefs.current[cardIndex] = el)}
                  className={`${styles.subCard} ${styles.animateCard}`}
                >
                  <h4>{sub.name}</h4>
                  <p className={styles.subDescription}>{sub.desc}</p>

                  {sub.features && (
                    <ul className={styles.featureList}>
                      {sub.features.slice(0, 4).map((feature, idx) => (
                        <li key={idx} className={styles.featureItem}>
                          • {feature}
                        </li>
                      ))}
                    </ul>
                  )}

                  {openInNewTab ? (
                    <a
                      href={href}
                      className={styles.subButton}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {sub.cta}
                    </a>
                  ) : (
                    <a href={href} className={styles.subButton}>
                      {sub.cta}
                    </a>
                  )}
                </div>
              );
            })}
          </div>

          {index < services.length - 1 && <div className={styles.divider}>⸻</div>}
        </div>
      ))}

      <div className={styles.contactSection}>
        <h3 className={styles.contactTitle}>Contact Us</h3>
        <p className={styles.contactDescription}>
          Ready to get started? Let's discuss how Baltar Inc. can help transform your business.
        </p>
        <a href="/contact" className={styles.contactButton}>
          Get In Touch
        </a>
      </div>
    </section>
  );
}
