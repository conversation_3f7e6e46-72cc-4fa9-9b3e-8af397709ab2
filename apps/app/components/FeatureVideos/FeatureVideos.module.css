/* Meta-inspired Feature Section */
.featureSection {
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
  color: #1c2b33;
  padding: 8rem 2rem;
  text-align: center;
  position: relative;
}

.sectionHeader {
  max-width: 800px;
  margin: 0 auto 6rem;
}

.sectionHeading {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.sectionSubheading {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  font-weight: 600;
  color: #4267b2;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.sectionDescription {
  font-size: 1.125rem;
  line-height: 1.6;
  color: #65676b;
  max-width: 600px;
  margin: 0 auto;
}

.serviceBlock {
  margin-bottom: 8rem;
  position: relative;
}

.serviceHeader {
  margin-bottom: 3rem;
}

.serviceTitle {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 0.75rem;
  font-weight: 700;
  color: #1c2b33;
  letter-spacing: -0.01em;
}

.serviceTagline {
  font-size: 1.125rem;
  color: #4267b2;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.video {
  width: 85%;
  max-width: 1000px;
  margin: 0 auto 3rem;
  border-radius: 1.5rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.zoomIn {
  transform: scale(1.03);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.15),
    0 12px 24px rgba(0, 0, 0, 0.12);
}

.subsidiaries {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.subCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 2.5rem;
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(24, 119, 242, 0.1);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.subCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1877f2 0%, #42a5f5 100%);
}

.subCard:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08);
  border-color: rgba(24, 119, 242, 0.2);
}

.subCard h4 {
  font-size: 1.375rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1c2b33;
  letter-spacing: -0.01em;
}

.subDescription {
  font-size: 1rem;
  color: #65676b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.featureItem {
  font-size: 0.9rem;
  color: #8a8d91;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  padding-left: 0.5rem;
}

.subButton {
  display: inline-flex;
  align-items: center;
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
  border: none;
  cursor: pointer;
}

.subButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 119, 242, 0.4);
  background: linear-gradient(135deg, #166fe5 0%, #3b9ae1 100%);
}

.divider {
  font-size: 2rem;
  color: #bcc0c4;
  margin: 4rem 0;
  font-weight: 300;
}

.contactSection {
  margin-top: 6rem;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
  border-radius: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.contactTitle {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1c2b33;
}

.contactDescription {
  font-size: 1.125rem;
  color: #65676b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.contactButton {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.125rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 6px 16px rgba(24, 119, 242, 0.3);
}

.contactButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(24, 119, 242, 0.4);
  background: linear-gradient(135deg, #166fe5 0%, #3b9ae1 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .featureSection {
    padding: 4rem 1rem;
  }

  .subsidiaries {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .subCard {
    padding: 2rem;
  }

  .video {
    width: 95%;
  }

  .serviceBlock {
    margin-bottom: 5rem;
  }

  .contactSection {
    padding: 3rem 1.5rem;
    margin-top: 4rem;
  }
}

@media (max-width: 480px) {
  .sectionHeader {
    margin-bottom: 4rem;
  }

  .serviceHeader {
    margin-bottom: 2rem;
  }

  .subCard {
    padding: 1.5rem;
  }

  .featureItem {
    font-size: 0.85rem;
  }
}

/* Advanced Animations */
.animateCard {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slideInUp {
  opacity: 1;
  transform: translateY(0);
}

/* Parallax and Scroll Effects */
.featureSection {
  will-change: transform;
}

/* Enhanced Hover Animations */
.subCard {
  position: relative;
  overflow: hidden;
}

.subCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(24, 119, 242, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.subCard:hover::after {
  left: 100%;
}

/* Staggered Animation Delays */
.animateCard:nth-child(1) { transition-delay: 0.1s; }
.animateCard:nth-child(2) { transition-delay: 0.2s; }
.animateCard:nth-child(3) { transition-delay: 0.3s; }
.animateCard:nth-child(4) { transition-delay: 0.4s; }

/* Video Hover Effects */
.video:hover {
  filter: brightness(1.1) contrast(1.05);
}

/* Button Pulse Animation */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
  }
  50% {
    box-shadow: 0 8px 20px rgba(24, 119, 242, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
  }
}

.subButton {
  animation: pulse 2s infinite;
}

.subButton:hover {
  animation: none;
}

/* Smooth Scroll Behavior - moved to globals.css */

/* Loading Animation for Cards */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.subCard.loading {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
