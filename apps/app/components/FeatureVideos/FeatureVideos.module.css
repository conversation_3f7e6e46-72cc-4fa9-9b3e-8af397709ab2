/* Elegant Feature Section */
.featureSection {
  background: var(--background);
  color: var(--text-primary);
  padding: 10rem 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.featureSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.sectionHeader {
  max-width: 1000px;
  margin: 0 auto 8rem;
  position: relative;
  z-index: 1;
}

.sectionHeading {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 900;
  margin-bottom: 2rem;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.03em;
  line-height: 1.1;
}

.sectionSubheading {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
  font-weight: 700;
  background: var(--gradient-secondary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.sectionDescription {
  font-size: clamp(1.125rem, 2.5vw, 1.375rem);
  line-height: 1.8;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.9;
}

.serviceBlock {
  margin-bottom: 12rem;
  position: relative;
  z-index: 1;
}

.serviceHeader {
  margin-bottom: 4rem;
}

.serviceTitle {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1rem;
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.serviceTagline {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  background: var(--gradient-accent);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
}

.video {
  width: 90%;
  max-width: 1200px;
  aspect-ratio: 16/9;
  margin: 0 auto 4rem;
  border-radius: 2rem;
  box-shadow: var(--shadow-2xl);
  transition: all 0.8s var(--ease-out-expo);
  border: 2px solid var(--glass-border);
  background: var(--background-secondary);
  object-fit: cover;
  overflow: hidden;
}

.video::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.zoomIn {
  transform: scale(1.02) translateY(-10px);
  box-shadow:
    var(--shadow-2xl),
    0 0 60px rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.5);
}

.subsidiaries {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

.subCard {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 2rem;
  box-shadow: var(--glass-shadow);
  transition: all 0.6s var(--ease-out-expo);
  border: 1px solid var(--glass-border);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.subCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2rem 2rem 0 0;
}

.subCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.subCard:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    var(--shadow-2xl),
    0 0 40px rgba(99, 102, 241, 0.4);
  border-color: rgba(99, 102, 241, 0.6);
}

.subCard:hover::after {
  opacity: 0.05;
}

.subCard h4 {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  font-weight: 800;
  margin-bottom: 1.25rem;
  color: var(--text-primary);
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.subDescription {
  font-size: 1rem;
  color: #65676b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.featureItem {
  font-size: 0.9rem;
  color: #8a8d91;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  padding-left: 0.5rem;
}

.subButton {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-radius: 1rem;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.4s var(--ease-out-expo);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--glass-border);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.subButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.subButton:hover::before {
  left: 100%;
}

.subButton:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-2xl);
  background: var(--gradient-secondary);
}

.divider {
  font-size: 2.5rem;
  background: var(--gradient-accent);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 6rem 0;
  font-weight: 300;
  opacity: 0.7;
}

.contactSection {
  margin-top: 8rem;
  padding: 5rem 3rem;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 2.5rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.contactSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.05;
  border-radius: inherit;
}

.contactTitle {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 900;
  margin-bottom: 1.5rem;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.contactDescription {
  font-size: clamp(1.125rem, 2.5vw, 1.375rem);
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.8;
  opacity: 0.9;
}

.contactButton {
  display: inline-flex;
  align-items: center;
  padding: 1.25rem 2.5rem;
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-radius: 1rem;
  font-weight: 700;
  font-size: 1.125rem;
  text-decoration: none;
  transition: all 0.4s var(--ease-out-expo);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--glass-border);
  position: relative;
  overflow: hidden;
}

.contactButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.contactButton:hover::before {
  left: 100%;
}

.contactButton:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: var(--shadow-2xl);
  background: var(--gradient-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .featureSection {
    padding: 4rem 1rem;
  }

  .subsidiaries {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .subCard {
    padding: 2rem;
  }

  .video {
    width: 95%;
  }

  .serviceBlock {
    margin-bottom: 5rem;
  }

  .contactSection {
    padding: 3rem 1.5rem;
    margin-top: 4rem;
  }
}

@media (max-width: 480px) {
  .sectionHeader {
    margin-bottom: 4rem;
  }

  .serviceHeader {
    margin-bottom: 2rem;
  }

  .subCard {
    padding: 1.5rem;
  }

  .featureItem {
    font-size: 0.85rem;
  }
}

/* Beautiful Consistent Animations */
.animateCard {
  opacity: 0;
  transform: translateY(80px) scale(0.9);
  transition: all 1s var(--ease-out-expo);
}

.slideInUp {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Parallax and Scroll Effects */
.featureSection {
  will-change: transform;
}

/* Enhanced Shimmer Effect */
.subCard {
  position: relative;
  overflow: hidden;
}

.subCard::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(99, 102, 241, 0.1) 50%,
    transparent 70%
  );
  transform: rotate(45deg);
  transition: transform 0.8s ease;
  opacity: 0;
}

.subCard:hover::before {
  transform: rotate(45deg) translate(50%, 50%);
  opacity: 1;
}

/* Staggered Animation Delays */
.animateCard:nth-child(1) { transition-delay: 0.1s; }
.animateCard:nth-child(2) { transition-delay: 0.3s; }
.animateCard:nth-child(3) { transition-delay: 0.5s; }
.animateCard:nth-child(4) { transition-delay: 0.7s; }

/* Video Hover Effects */
.video:hover {
  filter: brightness(1.1) contrast(1.1) saturate(1.2);
}

/* Elegant Button Animation */
@keyframes elegantPulse {
  0%, 100% {
    box-shadow:
      0 4px 20px rgba(99, 102, 241, 0.4),
      0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  50% {
    box-shadow:
      0 8px 30px rgba(99, 102, 241, 0.6),
      0 0 0 10px rgba(99, 102, 241, 0);
  }
}

.subButton {
  animation: elegantPulse 3s infinite;
}

.subButton:hover {
  animation: none;
  transform: translateY(-3px) scale(1.05);
}

/* Floating Animation for Service Blocks */
@keyframes floatService {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.serviceBlock:hover {
  animation: floatService 2s ease-in-out infinite;
}

/* Gradient Animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.sectionHeading,
.serviceTitle {
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

/* Loading Skeleton Animation */
@keyframes elegantShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.subCard.loading {
  background: linear-gradient(
    90deg,
    var(--background-secondary) 25%,
    var(--background-tertiary) 50%,
    var(--background-secondary) 75%
  );
  background-size: 200px 100%;
  animation: elegantShimmer 2s infinite;
}

/* Smooth entrance for section header */
.sectionHeader {
  animation: fadeInScale 1.2s var(--ease-out-expo) forwards;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
