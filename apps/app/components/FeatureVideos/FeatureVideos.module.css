/* Meta-style Clean Feature Section */
.featureSection {
  background: var(--background);
  color: var(--text-primary);
  padding: 80px 24px;
  text-align: center;
  position: relative;
}

.sectionHeader {
  max-width: 800px;
  margin: 0 auto 80px;
  position: relative;
}

.sectionHeading {
  font-size: clamp(40px, 6vw, 64px);
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.sectionSubheading {
  font-size: clamp(24px, 4vw, 32px);
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 16px;
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.sectionDescription {
  font-size: clamp(16px, 2.5vw, 20px);
  line-height: 1.34;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.serviceBlock {
  margin-bottom: 120px;
  position: relative;
}

.serviceHeader {
  margin-bottom: 48px;
}

.serviceTitle {
  font-size: clamp(32px, 5vw, 48px);
  margin-bottom: 8px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.01em;
  line-height: 1.1;
}

.serviceTagline {
  font-size: clamp(14px, 2.5vw, 16px);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.video {
  width: 100%;
  max-width: 800px;
  aspect-ratio: 16/9;
  margin: 0 auto 48px;
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s var(--ease-meta);
  border: 1px solid var(--border);
  background: var(--background-secondary);
  object-fit: cover;
}

.zoomIn {
  transform: scale(1.02);
  box-shadow: var(--shadow-xl);
}

.subsidiaries {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.subCard {
  background: var(--surface);
  padding: 32px;
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.2s var(--ease-meta);
  border: 1px solid var(--border);
  text-align: left;
  position: relative;
}

.subCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.subCard h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.subDescription {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  line-height: 1.34;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0 0 24px 0;
}

.featureItem {
  font-size: 14px;
  color: var(--text-muted);
  margin-bottom: 8px;
  line-height: 1.34;
  padding-left: 8px;
}

.subButton {
  display: inline-flex;
  align-items: center;
  padding: 12px 24px;
  background: var(--primary);
  color: var(--text-inverse);
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  transition: all 0.2s var(--ease-meta);
  border: none;
  cursor: pointer;
}

.subButton:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.divider {
  font-size: 24px;
  color: var(--text-muted);
  margin: 80px 0;
  font-weight: 300;
}

.contactSection {
  margin-top: 80px;
  padding: 64px 32px;
  background: var(--background-secondary);
  border-radius: 12px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid var(--border);
}

.contactTitle {
  font-size: clamp(32px, 5vw, 48px);
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

.contactDescription {
  font-size: clamp(16px, 2.5vw, 20px);
  color: var(--text-secondary);
  margin-bottom: 32px;
  line-height: 1.34;
}

.contactButton {
  display: inline-flex;
  align-items: center;
  padding: 16px 32px;
  background: var(--primary);
  color: var(--text-inverse);
  border-radius: 8px;
  font-weight: 600;
  font-size: 18px;
  text-decoration: none;
  transition: all 0.2s var(--ease-meta);
  border: none;
}

.contactButton:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .featureSection {
    padding: 4rem 1rem;
  }

  .subsidiaries {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .subCard {
    padding: 2rem;
  }

  .video {
    width: 95%;
  }

  .serviceBlock {
    margin-bottom: 5rem;
  }

  .contactSection {
    padding: 3rem 1.5rem;
    margin-top: 4rem;
  }
}

@media (max-width: 480px) {
  .sectionHeader {
    margin-bottom: 4rem;
  }

  .serviceHeader {
    margin-bottom: 2rem;
  }

  .subCard {
    padding: 1.5rem;
  }

  .featureItem {
    font-size: 0.85rem;
  }
}

/* Clean Meta-style Animations */
.animateCard {
  opacity: 0;
  transform: translateY(24px);
  transition: all 0.6s var(--ease-meta);
}

.slideInUp {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered Animation Delays */
.animateCard:nth-child(1) { transition-delay: 0.1s; }
.animateCard:nth-child(2) { transition-delay: 0.2s; }
.animateCard:nth-child(3) { transition-delay: 0.3s; }
.animateCard:nth-child(4) { transition-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .featureSection {
    padding: 60px 16px;
  }

  .subsidiaries {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .subCard {
    padding: 24px;
  }

  .serviceBlock {
    margin-bottom: 80px;
  }

  .contactSection {
    padding: 48px 24px;
    margin-top: 60px;
  }

  .sectionHeader {
    margin-bottom: 60px;
  }

  .serviceHeader {
    margin-bottom: 32px;
  }
}
