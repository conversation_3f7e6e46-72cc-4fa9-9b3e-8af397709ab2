/* Meta-style Clean Hero Section */
.heroSection {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
  padding: 80px 0;
}

.backgroundVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  opacity: 0.1;
  filter: brightness(1.2) saturate(0.8);
}

.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
  z-index: 2;
}

.contentContainer {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 1200px;
  padding: 0 24px;
}

.heroContent {
  text-align: center;
  color: var(--text-primary);
}

.heroTitle {
  font-size: clamp(48px, 8vw, 80px);
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.heroSubtitle {
  font-size: clamp(28px, 5vw, 48px);
  font-weight: 600;
  margin-bottom: 24px;
  color: var(--primary);
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.heroDescription {
  font-size: clamp(18px, 2.5vw, 24px);
  line-height: 1.34;
  max-width: 800px;
  margin: 0 auto;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Clean Meta-style Animations */
.fadeInUp {
  opacity: 0;
  transform: translateY(32px);
  transition: all 0.8s var(--ease-meta);
}

.fadeInUp.animate {
  opacity: 1;
  transform: translateY(0);
}

.fadeInUp:nth-child(1) {
  transition-delay: 0.1s;
}

.fadeInUp:nth-child(2) {
  transition-delay: 0.2s;
}

.fadeInUp:nth-child(3) {
  transition-delay: 0.3s;
}

/* Minimal Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  overflow: hidden;
}

.floatingOrb1,
.floatingOrb2,
.floatingOrb3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(24, 119, 242, 0.05);
  border: 1px solid rgba(24, 119, 242, 0.1);
}

.floatingOrb1 {
  width: 200px;
  height: 200px;
  top: 20%;
  right: 15%;
  animation: floatClean 6s ease-in-out infinite;
  animation-delay: 0s;
}

.floatingOrb2 {
  width: 120px;
  height: 120px;
  bottom: 30%;
  left: 10%;
  animation: floatClean 8s ease-in-out infinite reverse;
  animation-delay: 2s;
}

.floatingOrb3 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 30%;
  animation: floatClean 10s ease-in-out infinite;
  animation-delay: 4s;
}

@keyframes floatClean {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contentContainer {
    padding: 0 1rem;
  }
  
  .heroDescription {
    font-size: 1rem;
  }
  
  .floatingOrb1,
  .floatingOrb2,
  .floatingOrb3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    margin-bottom: 0.5rem;
  }
  
  .heroSubtitle {
    margin-bottom: 1.5rem;
  }
}
