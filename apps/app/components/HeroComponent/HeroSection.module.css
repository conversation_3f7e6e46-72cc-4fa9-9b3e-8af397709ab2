/* Meta-inspired Hero Section */
.heroSection {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 50%, #0d47a1 100%);
}

.backgroundVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  opacity: 0.3;
}

.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(24, 119, 242, 0.9) 0%,
    rgba(66, 165, 245, 0.8) 50%,
    rgba(13, 71, 161, 0.9) 100%
  );
  z-index: 2;
}

.contentContainer {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 1200px;
  padding: 0 2rem;
}

.heroContent {
  text-align: center;
  color: white;
}

.heroTitle {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.heroSubtitle {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 600;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.01em;
}

.heroDescription {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  font-weight: 400;
}

/* Animation Classes */
.fadeInUp {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fadeInUp.animate {
  opacity: 1;
  transform: translateY(0);
}

.fadeInUp:nth-child(1) {
  transition-delay: 0.2s;
}

.fadeInUp:nth-child(2) {
  transition-delay: 0.4s;
}

.fadeInUp:nth-child(3) {
  transition-delay: 0.6s;
}

/* Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.floatingOrb1,
.floatingOrb2,
.floatingOrb3 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.floatingOrb1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.floatingOrb2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.floatingOrb3 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contentContainer {
    padding: 0 1rem;
  }
  
  .heroDescription {
    font-size: 1rem;
  }
  
  .floatingOrb1,
  .floatingOrb2,
  .floatingOrb3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    margin-bottom: 0.5rem;
  }
  
  .heroSubtitle {
    margin-bottom: 1.5rem;
  }
}
