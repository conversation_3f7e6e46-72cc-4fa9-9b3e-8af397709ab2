/* Elegant Hero Section */
.heroSection {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
}

.backgroundVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  opacity: 0.4;
  filter: brightness(0.7) contrast(1.2) saturate(1.1);
}

.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, rgba(10, 10, 11, 0.8) 0%, rgba(17, 17, 19, 0.9) 100%);
  z-index: 2;
}

.contentContainer {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 1400px;
  padding: 0 3rem;
}

.heroContent {
  text-align: center;
  color: var(--text-primary);
}

.heroTitle {
  font-size: clamp(4rem, 10vw, 8rem);
  font-weight: 900;
  margin-bottom: 1.5rem;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.03em;
  line-height: 0.9;
  text-shadow: 0 0 40px rgba(99, 102, 241, 0.5);
  position: relative;
}

.heroTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.heroSubtitle {
  font-size: clamp(1.75rem, 5vw, 3rem);
  font-weight: 700;
  margin-bottom: 2.5rem;
  background: var(--gradient-secondary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.heroDescription {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
  line-height: 1.8;
  max-width: 900px;
  margin: 0 auto;
  color: var(--text-secondary);
  font-weight: 400;
  opacity: 0.9;
}

/* Elegant Animation Classes */
.fadeInUp {
  opacity: 0;
  transform: translateY(60px) scale(0.95);
  transition: all 1.2s var(--ease-out-expo);
}

.fadeInUp.animate {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.fadeInUp:nth-child(1) {
  transition-delay: 0.3s;
}

.fadeInUp:nth-child(2) {
  transition-delay: 0.6s;
}

.fadeInUp:nth-child(3) {
  transition-delay: 0.9s;
}

/* Sophisticated Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  overflow: hidden;
}

.floatingOrb1,
.floatingOrb2,
.floatingOrb3 {
  position: absolute;
  border-radius: 50%;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.floatingOrb1 {
  width: 300px;
  height: 300px;
  top: 15%;
  right: 10%;
  background: radial-gradient(circle at 30% 30%, rgba(99, 102, 241, 0.3), rgba(6, 182, 212, 0.1));
  animation: floatElegant 8s ease-in-out infinite;
  animation-delay: 0s;
}

.floatingOrb2 {
  width: 200px;
  height: 200px;
  bottom: 25%;
  left: 8%;
  background: radial-gradient(circle at 70% 70%, rgba(6, 182, 212, 0.3), rgba(139, 92, 246, 0.1));
  animation: floatElegant 10s ease-in-out infinite reverse;
  animation-delay: 2s;
}

.floatingOrb3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 25%;
  background: radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.3), rgba(99, 102, 241, 0.1));
  animation: floatElegant 12s ease-in-out infinite;
  animation-delay: 4s;
}

@keyframes floatElegant {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.05);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg) scale(0.95);
  }
  75% {
    transform: translateY(-40px) translateX(10px) rotate(270deg) scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contentContainer {
    padding: 0 1rem;
  }
  
  .heroDescription {
    font-size: 1rem;
  }
  
  .floatingOrb1,
  .floatingOrb2,
  .floatingOrb3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    margin-bottom: 0.5rem;
  }
  
  .heroSubtitle {
    margin-bottom: 1.5rem;
  }
}
