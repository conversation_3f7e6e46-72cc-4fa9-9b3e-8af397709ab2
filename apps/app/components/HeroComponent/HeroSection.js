'use client';
import { useEffect, useRef } from 'react';
import styles from './HeroSection.module.css';

export default function HeroSection() {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const descriptionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add(styles.animate);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (titleRef.current) observer.observe(titleRef.current);
    if (subtitleRef.current) observer.observe(subtitleRef.current);
    if (descriptionRef.current) observer.observe(descriptionRef.current);

    return () => {
      if (titleRef.current) observer.unobserve(titleRef.current);
      if (subtitleRef.current) observer.unobserve(subtitleRef.current);
      if (descriptionRef.current) observer.unobserve(descriptionRef.current);
    };
  }, []);

  return (
    <section ref={heroRef} className={styles.heroSection}>
      {/* Background Video */}
      <video
        autoPlay
        muted
        loop
        playsInline
        className={styles.backgroundVideo}
      >
        <source src="/video.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Gradient Overlay */}
      <div className={styles.gradientOverlay} />

      {/* Content Container */}
      <div className={styles.contentContainer}>
        <div className={styles.heroContent}>
          <h1 ref={titleRef} className={`${styles.heroTitle} ${styles.fadeInUp}`}>
            Baltar Inc.
          </h1>

          <h2 ref={subtitleRef} className={`${styles.heroSubtitle} ${styles.fadeInUp}`}>
            One Company. Limitless Services.
          </h2>

          <p ref={descriptionRef} className={`${styles.heroDescription} ${styles.fadeInUp}`}>
            Baltar Inc. is a multi-division firm offering advanced solutions across construction,
            technology, hospitality, finance, fashion, and media. We blend innovation with
            execution—supporting startups, professionals, and enterprise clients across Canada.
          </p>
        </div>
      </div>

      {/* Floating Elements */}
      <div className={styles.floatingElements}>
        <div className={styles.floatingOrb1}></div>
        <div className={styles.floatingOrb2}></div>
        <div className={styles.floatingOrb3}></div>
      </div>
    </section>
  );
}
