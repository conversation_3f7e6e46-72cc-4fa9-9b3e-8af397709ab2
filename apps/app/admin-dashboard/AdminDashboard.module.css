.dashboardContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  color: white;
}

.headerContent h1 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.headerContent p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.refreshButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.logoutButton {
  background: rgba(239, 68, 68, 0.8);
  border: 1px solid rgba(239, 68, 68, 0.9);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logoutButton:hover {
  background: rgba(239, 68, 68, 1);
  transform: translateY(-2px);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  text-align: center;
}

.statCard h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.tabNavigation {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.5rem;
}

.tab {
  flex: 1;
  padding: 1rem 2rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

.content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  min-height: 600px;
}

.overviewGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.overviewSection {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.overviewSection h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.itemList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.listItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.itemInfo h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-weight: 600;
}

.itemInfo p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.itemDate {
  font-size: 0.75rem;
  color: #9ca3af;
}

.itemStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

.tableContainer {
  overflow-x: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.table tr:hover {
  background: #f9fafb;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.actionButton {
  background: #10B981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background: #059669;
  transform: translateY(-1px);
}

.deleteButton {
  background: #EF4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deleteButton:hover {
  background: #DC2626;
  transform: translateY(-1px);
}

.editButton {
  background: #3B82F6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.editButton:hover {
  background: #2563EB;
  transform: translateY(-1px);
}

/* Project Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.projectModal {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.projectModal h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  text-align: center;
}

.projectModal h4 {
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.modalForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.formInput,
.formTextarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.formInput:focus,
.formTextarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formTextarea {
  resize: vertical;
  min-height: 80px;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.saveButton {
  background: #10B981;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.saveButton:hover {
  background: #059669;
  transform: translateY(-2px);
}

.cancelButton {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: #4b5563;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .headerContent h1 {
    font-size: 2rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tabNavigation {
    flex-direction: column;
  }

  .tab {
    padding: 0.75rem 1rem;
  }

  .overviewGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .listItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .itemStatus {
    align-self: flex-end;
  }

  .table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }

  .modalOverlay {
    padding: 1rem;
  }

  .projectModal {
    padding: 1.5rem;
    max-height: 95vh;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .modalActions {
    flex-direction: column;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.25rem;
  }
}
