@tailwind base;
@tailwind components;
@tailwind utilities;

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Elegant Color Palette */
  --background: #0a0a0b;
  --background-secondary: #111113;
  --background-tertiary: #1a1a1d;
  --foreground: #ffffff;

  /* Primary Colors - Sophisticated Purple/Blue Gradient */
  --primary: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #a5b4fc;
  --primary-dark: #3730a3;

  /* Secondary Colors - Elegant Teal/Cyan */
  --secondary: #06b6d4;
  --secondary-hover: #0891b2;
  --secondary-light: #67e8f9;
  --secondary-dark: #0e7490;

  /* Accent Colors */
  --accent-gold: #f59e0b;
  --accent-rose: #f43f5e;
  --accent-emerald: #10b981;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --text-inverse: #111827;

  /* Surface Colors */
  --surface: rgba(255, 255, 255, 0.05);
  --surface-hover: rgba(255, 255, 255, 0.1);
  --surface-active: rgba(255, 255, 255, 0.15);

  /* Border Colors */
  --border: rgba(255, 255, 255, 0.1);
  --border-hover: rgba(255, 255, 255, 0.2);

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.7);

  /* Gradient Definitions */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #6366f1 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #f43f5e 50%, #8b5cf6 100%);
  --gradient-surface: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Animation Timing */
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--background);
  color: var(--foreground);
  line-height: 1.7;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Elegant scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.01em;
  margin: 0;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Smooth Transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Form Improvements */
.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-primary);
  border: 2px solid var(--border);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Selection Styling */
::selection {
  background-color: var(--primary);
  color: white;
}

::-moz-selection {
  background-color: var(--primary);
  color: white;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #4f46e5;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background-color: #4338ca;
}

html {
  scroll-behavior: smooth;
}
