@tailwind base;
@tailwind components;
@tailwind utilities;

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --background: #ffffff;
  --foreground: #1c2b33;
  --primary: #1877f2;
  --primary-hover: #166fe5;
  --secondary: #42a5f5;
  --accent: #e3f2fd;
  --text-primary: #1c2b33;
  --text-secondary: #65676b;
  --text-muted: #8a8d91;
  --border: rgba(24, 119, 242, 0.1);
  --shadow: rgba(0, 0, 0, 0.08);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #18191a;
    --foreground: #e4e6ea;
    --primary: #1877f2;
    --primary-hover: #166fe5;
    --secondary: #42a5f5;
    --accent: #242526;
    --text-primary: #e4e6ea;
    --text-secondary: #b0b3b8;
    --text-muted: #8a8d91;
    --border: rgba(255, 255, 255, 0.1);
    --shadow: rgba(0, 0, 0, 0.3);
  }
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.01em;
  margin: 0;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Smooth Transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Form Improvements */
.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-primary);
  border: 2px solid var(--border);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Selection Styling */
::selection {
  background-color: var(--primary);
  color: white;
}

::-moz-selection {
  background-color: var(--primary);
  color: white;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #4f46e5;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background-color: #4338ca;
}

html {
  scroll-behavior: smooth;
}
