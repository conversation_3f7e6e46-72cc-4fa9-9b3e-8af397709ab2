@tailwind base;
@tailwind components;
@tailwind utilities;

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Meta's Clean Color Palette */
  --background: #ffffff;
  --background-secondary: #f8f9fa;
  --background-tertiary: #e9ecef;
  --foreground: #1c1e21;

  /* Meta Blue - Primary Colors */
  --primary: #1877f2;
  --primary-hover: #166fe5;
  --primary-light: #e7f3ff;
  --primary-dark: #1565c0;

  /* Secondary Colors - Clean Grays */
  --secondary: #42a5f5;
  --secondary-hover: #1e88e5;
  --secondary-light: #e3f2fd;
  --secondary-dark: #1565c0;

  /* Text Colors - Meta Style */
  --text-primary: #1c1e21;
  --text-secondary: #65676b;
  --text-muted: #8a8d91;
  --text-inverse: #ffffff;

  /* Surface Colors - Clean and Minimal */
  --surface: #ffffff;
  --surface-hover: #f8f9fa;
  --surface-active: #e4e6ea;

  /* Border Colors - Subtle */
  --border: #dadde1;
  --border-hover: #ccd0d5;
  --border-light: #e4e6ea;

  /* Shadow Colors - Soft and Clean */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-xl: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Clean Gradients */
  --gradient-primary: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  --gradient-secondary: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  --gradient-hero: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);

  /* Animation Timing - Smooth and Natural */
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-meta: cubic-bezier(0.17, 0.17, 0, 1);
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: var(--background);
  color: var(--foreground);
  line-height: 1.34;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

/* Clean scrollbar like Meta */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.01em;
  margin: 0;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Smooth Transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Form Improvements */
.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-primary);
  border: 2px solid var(--border);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Selection Styling */
::selection {
  background-color: var(--primary);
  color: white;
}

::-moz-selection {
  background-color: var(--primary);
  color: white;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #4f46e5;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background-color: #4338ca;
}

html {
  scroll-behavior: smooth;
}
