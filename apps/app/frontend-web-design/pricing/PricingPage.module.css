.pricingPage {
  min-height: 100vh;
  background: white;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

/* Pricing Section */
.pricingSection {
  padding: 6rem 0;
  background: white;
}

.pricingGrid {
  display: flex;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: stretch;
}

.pricingCard {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 2.5rem;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 380px;
  min-height: 700px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.pricingCard:hover {
  border-color: #667eea;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
}

.pricingCard.popular {
  border-color: #667eea;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.15);
  transform: scale(1.05);
}

.popularBadge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.planHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.planName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.planPrice {
  margin-bottom: 1.5rem;
}

.price {
  font-size: 3rem;
  font-weight: 700;
  color: #1e293b;
}

.period {
  font-size: 1rem;
  color: #64748b;
  margin-left: 0.5rem;
}

.planDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
  margin-bottom: 2rem;
}

.planFeatures {
  flex-grow: 1;
  margin-bottom: 2rem;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 0.75rem;
}

.checkmark {
  color: #10b981;
  font-weight: bold;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.planButton {
  width: auto;
  max-width: 200px;
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: block;
  text-align: center;
  margin-top: auto;
  margin-left: auto;
  margin-right: auto;
}

.planButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.planButton.popularButton {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

.planButton.popularButton:hover {
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Add-ons Section */
.addOnsSection {
  padding: 6rem 0;
  background: #f8fafc;
}

.addOnsHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.addOnsTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.addOnsSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

.addOnsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.addOnItem {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.addOnItem:hover {
  border-color: #667eea;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.addOnName {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 500;
}

.addOnPrice {
  font-size: 1rem;
  color: #667eea;
  font-weight: 600;
}

/* Custom Section */
.customSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
}

.customContent {
  max-width: 600px;
  margin: 0 auto;
}

.customTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.customDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.95;
}

.customButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .pricingCard.popular {
    transform: none;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .pricingGrid {
    flex-direction: column;
    align-items: center;
  }

  .pricingCard {
    padding: 2rem;
    max-width: 100%;
    width: 100%;
  }
  
  .price {
    font-size: 2.5rem;
  }
  
  .addOnsGrid {
    grid-template-columns: 1fr;
  }
  
  .customTitle {
    font-size: 2rem;
  }
  
  .customButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .pricingCard {
    padding: 1.5rem;
  }
  
  .price {
    font-size: 2rem;
  }
  
  .addOnItem {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
